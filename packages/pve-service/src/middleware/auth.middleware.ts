import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../redis/redis.service';
import * as jwt from 'jsonwebtoken';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuthMiddleware.name);

  constructor(
    private configService: ConfigService,
    private redisService: RedisService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const path = req.baseUrl;
    const ignoreUrls = this.configService.get<string[]>('ignoreUrls');

    if (ignoreUrls.some((url) => path.startsWith(url))) {
      return next();
    }

    const token = req.headers.authorization;
    if (!token) {
      return res.status(401).json({
        code: 401,
        message: '未提供认证令牌',
      });
    }

    try {
      const jwtSecret = this.configService.get<string>('jwt.secret');
      const decoded = jwt.verify(token, jwtSecret) as any;
      const cacheKey = `admin:token:${decoded.userId}`;
      const rToken = await this.redisService.get(cacheKey);

      if (!rToken) {
        return res.status(401).json({
          code: 401,
          message: '登录已失效',
        });
      }

      if (rToken !== token) {
        return res.status(401).json({
          code: 401,
          message: '登录已失效',
        });
      }

      const passwordVKey = `admin:passwordVersion:${decoded.userId}`;
      const passwordV = await this.redisService.get(passwordVKey);

      if (passwordV != decoded.passwordVersion) {
        return res.status(401).json({
          code: 401,
          message: '登录已失效',
        });
      }

      const permsKey = `admin:perms:${decoded.userId}`;
      const perms: string[] = (await this.redisService.get(permsKey)) as any;

      if (decoded.username === 'admin' && !decoded.isRefresh) {
        req['user'] = decoded;
        return next();
      }

      if (perms && perms.length > 0) {
        const formattedPerms = perms.map((p) => p.replace(/:/g, '/'));
        let currentPath = path.split('?')[0].replace('/admin/', '');

        if (currentPath.startsWith('/')) {
          currentPath = currentPath.slice(1);
        }

        if (!formattedPerms.includes(currentPath)) {
          return res.status(403).json({
            code: 403,
            message: '无权限访问',
          });
        }
      } else {
        return res.status(403).json({
          code: 403,
          message: '无权限访问',
        });
      }

      req['user'] = decoded;
      next();
    } catch (error) {
      this.logger.error(`认证过程发生错误: ${error.message}`, error.stack);
      return res.status(401).json({
        code: 401,
        message: '无效的认证令牌',
      });
    }
  }
}

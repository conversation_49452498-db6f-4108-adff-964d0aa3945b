import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { DataSource } from 'typeorm';

import { logger } from '@/utils/logger';
import { dayjs } from '@/utils/dayjs';

@Injectable()
export class SubnetHistoryScheduleService implements OnModuleInit {
  TABLE_NAME = 'op_ads_subnet_flow_history';
  TABLE_SCHEMA = process.env.ADS_DB_DATABASE;
  PARTITION_FIELD = 'createTime';

  constructor(private readonly dataSource: DataSource) {}

  // 项目初始化函数：检测表是否存在分区，如果不存在则创建一个当天分区
  async onModuleInit() {
    const result = await this.dataSource.query(
      `SELECT TABLE_SCHEMA, TABLE_NAME, PARTITION_NAME FROM information_schema.PARTITIONS WHERE TABLE_SCHEMA = '${this.TABLE_SCHEMA}' AND TABLE_NAME = '${this.TABLE_NAME}';`,
    );

    // 是否存在分区
    const isExistPartition = result.some((item) => {
      return item.PARTITION_NAME !== null;
    });

    // 当天分区
    const isExistCurrentDayPartition = result.some((item) => {
      return item.PARTITION_NAME === `p${dayjs().format('YYYYMMDD')}`;
    });

    // 新增初始化分区
    if (!isExistPartition) {
      await this.dataSource.query(`
        ALTER TABLE ${this.TABLE_NAME}
        PARTITION BY RANGE (TO_DAYS(${this.PARTITION_FIELD})) (
          ${this.generatePartitionSQL()}
        );
      `);
    } else if (!isExistCurrentDayPartition) {
      await this.dataSource.query(`ALTER TABLE ${this.TABLE_NAME} ADD PARTITION (${this.generatePartitionSQL()});`);
    }
  }

  // 每天23:00执行一次新增分区
  @Cron(CronExpression.EVERY_DAY_AT_11PM)
  async addNextDayPartition() {
    try {
      await this.dataSource.query(`ALTER TABLE ${this.TABLE_NAME} ADD PARTITION (${this.generatePartitionSQL(1)});`);
    } catch (error) {
      logger.error(`新增NFD Subnet历史记录分区失败，错误信息：${error.message}`);
    }
  }

  // 每天00:00执行一次删除分歧
  // 7天前的分支
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async removePartition() {
    const PARTITION_NAME = `p${dayjs().subtract(7, 'days').format('YYYYMMDD')}`;
    const REMOVE_PARTITION_SQL = `ALTER TABLE ${this.TABLE_NAME} DROP PARTITION ${PARTITION_NAME};`;

    try {
      await this.dataSource.query(REMOVE_PARTITION_SQL);
    } catch (error) {
      logger.error(`移除NFD Subnet历史记录分区失败，错误信息：${error.message}`);
    }
  }

  generatePartitionSQL(value = 0): string {
    const now = dayjs().add(value, 'days');
    const partitionName = `p${now.format('YYYYMMDD')}`;
    const nextDay = now.add(1, 'days').format('YYYY-MM-DD');

    const sql = `
        PARTITION ${partitionName} VALUES LESS THAN (TO_DAYS('${nextDay}'))
    `;

    return sql.trim();
  }
}

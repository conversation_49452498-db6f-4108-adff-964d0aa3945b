import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import * as ip from 'ip';

import { IPAddress } from '../entities/ip';
import { IPSubnet } from '../entities/subnet';

@Injectable()
export class IpAddressAndSubnetService {
  constructor(
    @InjectRepository(IPAddress)
    private readonly ipAddressRepository: Repository<IPAddress>,

    @InjectRepository(IPSubnet)
    private readonly ipSubnetRepository: Repository<IPSubnet>,
  ) {}

  async getIpAddressList(ipAddressList: string[]) {
    return this.ipAddressRepository.find({
      where: { ipAddress: In(ipAddressList) },
    });
  }

  /**
   * 提取IP地址的前三段，用于24位子网匹配
   * @param ipAddress IP地址字符串
   * @returns 前三段IP地址
   */
  private extractFirstThreeOctets(ipAddress: string): string {
    const parts = ipAddress.split('.');
    if (parts.length === 4) {
      return `${parts[0]}.${parts[1]}.${parts[2]}`;
    }
    return ipAddress;
  }

  /**
   * 生成24位子网掩码的查询模式
   * @param firstThreeOctets IP地址的前三段
   * @returns 子网查询模式
   */
  private generateSubnetPattern(firstThreeOctets: string): string {
    return `${firstThreeOctets}.0`;
  }

  /**
   * 使用ip库生成24位子网
   * @param ipAddress IP地址
   * @returns 24位子网地址
   */
  private generateSubnet24(ipAddress: string): string {
    try {
      // 使用ip库计算24位子网
      const subnet = ip.subnet(ipAddress, '*************');
      return subnet.networkAddress;
    } catch (error) {
      // 如果ip库失败，回退到原来的方法
      return this.generateSubnetPattern(this.extractFirstThreeOctets(ipAddress));
    }
  }

  /**
   * 使用ip库生成更大范围的子网模式
   * @param subnet 原始子网地址 (如 "***********")
   * @returns 更大范围的子网模式数组
   */
  private generateLargerSubnetPatterns(subnet: string): string[] {
    const patterns: string[] = [];

    try {
      // 从24位开始，逐步减少到16位
      for (let i = 23; i >= 16; i--) {
        const largerSubnet = this.calculateLargerSubnetWithIpLib(subnet, i);
        patterns.push(largerSubnet);
      }
    } catch (error) {
      // 如果ip库失败，回退到原来的方法
      for (let i = 23; i >= 16; i--) {
        const largerSubnet = this.calculateLargerSubnet(subnet, i);
        patterns.push(largerSubnet);
      }
    }

    return patterns;
  }

  /**
   * 使用ip库计算更大范围的子网
   * @param subnetAddr 子网地址
   * @param newPrefixLength 新的前缀长度
   * @returns 更大范围的子网
   */
  private calculateLargerSubnetWithIpLib(subnetAddr: string, newPrefixLength: number): string {
    try {
      // 使用ip库计算子网
      const mask = ip.fromPrefixLen(newPrefixLength);
      const subnet = ip.subnet(subnetAddr, mask);
      return subnet.networkAddress;
    } catch (error) {
      // 如果ip库失败，回退到原来的方法
      return this.calculateLargerSubnet(subnetAddr, newPrefixLength);
    }
  }

  /**
   * 计算更大范围的子网 (备用方法)
   * @param subnetAddr 子网地址
   * @param newPrefixLength 新的前缀长度
   * @returns 更大范围的子网
   */
  private calculateLargerSubnet(subnetAddr: string, newPrefixLength: number): string {
    const parts = subnetAddr.split('.').map(Number);

    // 将IP地址转换为32位整数
    const ipInt = (parts[0] << 24) + (parts[1] << 16) + (parts[2] << 8) + parts[3];

    // 计算子网掩码
    const mask = (0xffffffff << (32 - newPrefixLength)) >>> 0;

    // 应用掩码得到网络地址
    const networkInt = ipInt & mask;

    // 转换回点分十进制格式
    const newParts = [
      (networkInt >>> 24) & 0xff,
      (networkInt >>> 16) & 0xff,
      (networkInt >>> 8) & 0xff,
      networkInt & 0xff,
    ];

    return newParts.join('.');
  }

  async getIpSubnetList(ipSubnetList: string[]) {
    // 首先尝试获取精确匹配的子网
    const exactMatches = await this.ipSubnetRepository.find({
      where: { subnet: In(ipSubnetList) },
    });

    // 找出未匹配的子网
    const missingSubnets = ipSubnetList.filter((subnet) => !exactMatches.find((item) => item.subnet === subnet));

    // 为缺失的子网生成更大范围的子网模式
    const largerSubnetPatterns: string[] = [];
    missingSubnets.forEach((subnet) => {
      const patterns = this.generateLargerSubnetPatterns(subnet);
      largerSubnetPatterns.push(...patterns);
    });

    // 查询更大范围的子网
    let largerSubnets: IPSubnet[] = [];
    if (largerSubnetPatterns.length > 0) {
      // 去重并查询
      const uniquePatterns = [...new Set(largerSubnetPatterns)];
      const foundLargerSubnets = await this.ipSubnetRepository.find({
        where: { subnet: In(uniquePatterns) },
      });

      // 为每个缺失的子网找到最匹配的更大范围子网
      const matchedLargerSubnets = missingSubnets
        .map((missingSubnet) => {
          const patterns = this.generateLargerSubnetPatterns(missingSubnet);

          // 按优先级排序（前缀长度越小，范围越大，优先级越低）
          // patterns 数组中的元素是按前缀长度从大到小排列的，所以直接使用
          const sortedPatterns = patterns;

          // 找到第一个匹配的更大范围子网
          for (const pattern of sortedPatterns) {
            const matchedSubnet = foundLargerSubnets.find((item) => item.subnet === pattern);
            if (matchedSubnet) {
              return matchedSubnet;
            }
          }
          return null;
        })
        .filter(Boolean) as IPSubnet[];

      largerSubnets = matchedLargerSubnets;
    }

    // 合并所有找到的子网
    const allFoundSubnets = [...exactMatches, ...largerSubnets];

    // 遍历输入的ipSubnetList，返回统一格式的结果
    return ipSubnetList.map((subnetString) => {
      // 查找精确匹配的子网
      const exactMatch = exactMatches.find((item) => item.subnet === subnetString);

      // 如果没有精确匹配，查找更大范围的匹配
      const largerMatch = !exactMatch
        ? largerSubnets.find((item) => {
            const patterns = this.generateLargerSubnetPatterns(subnetString);
            return patterns.includes(item.subnet);
          })
        : null;

      // 使用找到的子网信息（优先使用精确匹配）
      const subnetItem = exactMatch || largerMatch;

      // 返回统一格式的结果
      return {
        subnet: subnetString,
        customerNo: subnetItem?.customerNo,
        serviceNo: subnetItem?.serviceNo,
        serviceNo2: subnetItem?.serviceNo2,
        originAsn: subnetItem?.originAsn,
        location: subnetItem?.location,
        productName: subnetItem?.productName,
        description: subnetItem?.description,
      };
    });
  }

  async getIpAddressAndIpSubnetList(ipAddressStringList: string[]) {
    // 获取IP地址列表
    const ipAddressList = await this.getIpAddressList(ipAddressStringList);

    // 获取IP地址列表中opsIpSubnetId不为空的IP地址
    const opsIpSubnetIdList = ipAddressList
      .filter((item) => item.opsIpSubnetId !== null)
      .map((item) => item.opsIpSubnetId);

    // 获取IP地址列表中opsIpSubnetId不为空的IP地址对应的IP子网列表
    let ipSubnetList = [];
    if (opsIpSubnetIdList.length > 0) {
      ipSubnetList = await this.ipSubnetRepository.find({
        where: { opsIpSubnetId: In(opsIpSubnetIdList) },
      });
    }

    // 处理获取不到IP的情况，需要查找对应的24位子网
    const missingIpAddresses = ipAddressStringList.filter(
      (ipAddressString) => !ipAddressList.find((item) => item.ipAddress === ipAddressString),
    );

    // 为缺失的IP地址生成24位子网模式并查询
    const subnetPatterns = missingIpAddresses.map((ipAddress) => this.generateSubnet24(ipAddress));

    // 查询24位子网信息
    let subnet24List = [];
    if (subnetPatterns.length > 0) {
      subnet24List = await this.ipSubnetRepository.find({
        where: { subnet: In(subnetPatterns) },
      });
    }

    // 返回IP地址列表和IP子网列表
    return ipAddressStringList.map((ipAddressString) => {
      const ipAddressItem = ipAddressList.find((item) => {
        return item.ipAddress === ipAddressString;
      });

      const ipSubnetItem = ipAddressItem
        ? ipSubnetList.find((item) => {
            return ipAddressItem.opsIpSubnetId === item.opsIpSubnetId;
          })
        : null;

      // 如果IP地址不存在，查找对应的24位子网
      const subnet24Item = !ipAddressItem
        ? subnet24List.find((item) => {
            const pattern = this.generateSubnet24(ipAddressString);
            return item.subnet === pattern;
          })
        : null;

      // 按照优先级返回信息：ipAddressItem > ipSubnetItem > subnet24Item
      return {
        ipAddress: ipAddressString,
        customerNo: ipAddressItem?.customerNo || ipSubnetItem?.customerNo || subnet24Item?.customerNo,
        serviceNo: ipAddressItem?.serviceNo || ipSubnetItem?.serviceNo || subnet24Item?.serviceNo,
        serviceNo2: ipAddressItem?.serviceNo2 || ipSubnetItem?.serviceNo2 || subnet24Item?.serviceNo2,
        originAsn: ipSubnetItem?.originAsn || subnet24Item?.originAsn,
        location: ipSubnetItem?.location || subnet24Item?.location,
        productName: ipSubnetItem?.productName || subnet24Item?.productName,
        description: ipAddressItem?.description || subnet24Item?.description,
      };
    });
  }
}

import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { QueryRunner, Repository } from 'typeorm'
import { TransJournalEntity } from '../entity/journal'
import { ConfCustomNoService, Count } from '/$/conf/service/common'
import { TransJournalItemEntity } from '../entity/journal_item'
import * as _ from 'lodash'
import { isEmpty } from '/@/utils/types'

/**
 * 描述
 */
@Provide()
export class TransJournalService extends BaseService {
	@InjectEntityModel(TransJournalEntity)
	transJournalEntity: Repository<TransJournalEntity>

	@InjectEntityModel(TransJournalItemEntity)
	transJournalItemEntity: Repository<TransJournalItemEntity>

	@Inject()
	confCustomNoService: ConfCustomNoService

	@Inject()
	ctx

	/**
	 * 新增 journal
	 */
	@CoolTransaction()
	async addJournal(params, runner?: QueryRunner) {
		params.updateBy = this.ctx.admin.username
		params.createBy = this.ctx.admin.username

		return await this.confCustomNoService.handler(params, Count.TRANS_JOURNAL, TransJournalEntity, runner)
	}

	/**
	 * 添加 item
	 */
	@CoolTransaction()
	async addItems(params, journalId, runner?: QueryRunner) {
		const { username } = this.ctx.admin

		if (journalId) {
			const { items } = params

			return await runner.manager.insert<TransJournalItemEntity>(
				TransJournalItemEntity,
				items?.map((i) => ({
					...i,
					journalId,
					createBy: username,
					updateBy: username,
				}))
			)
		} else {
			throw new Error('journalId 不能为空')
		}
	}

	/**
	 * 详情
	 */
	async info(id) {
		if (id) {
			const journal = await this.transJournalEntity.findOne({ id })
			const items = await this.transJournalItemEntity.find({ journalId: id })
			return {
				...journal,
				items,
			}
		} else {
			throw new Error('未传递参数 id')
		}
	}

	/**
	 * 修改
	 */
	async update(params) {
		const { username } = this.ctx.admin
		const { id, items, ...data } = params
		// 修改 journal
		await this.transJournalEntity.update(
			{ id },
			{
				...data,
				updateBy: username,
			}
		)
		const addList = [],
			currentIds = [],
			updateList = []

		items.forEach((item) => {
			if (isEmpty(item)) return
			if (isEmpty(item?.id)) return addList.push(item)
			updateList.push({ ...item, updateBy: username })
			currentIds.push(item.id)
		})

		// 修改已有的 item
		const promises = updateList.map(async (item) => {
			return this.transJournalItemEntity.save(item)
		})
		await Promise.all(promises)

		// 查询需要移除的 item
		const sql = this.transJournalItemEntity.createQueryBuilder().select('id').where('journalId = :id', { id })

		if (!isEmpty(currentIds)) sql.andWhere('id NOT IN (:ids)', { ids: currentIds })

		const deleteList = await sql.getRawMany()

		// 添加新增的 item
		if (!isEmpty(addList)) {
			await this.addItems({ ...params, items: addList }, id)
		}

		// 删除
		if (isEmpty(deleteList)) return
		await this.transJournalItemEntity.delete(_.map(deleteList, 'id'))
	}

	/**
	 * 添加 manual 时自动添加 journal
	 */
	async manualAddJournal(params, transManual) {
		const {
			tranDate,
			description,
			counterAccountAmount,
			visibleRoleId,
			notes,
			tranCurrency,
			currentAccountId,
			customerId,
			vendorId,
			addJournal,
		} = params
		if (!addJournal) return
		const data = {
			journalDate: tranDate,
			description,
			transManualId: transManual.id,
			status: '',
			totalAmount: counterAccountAmount,
			visibleRoleId,
			notes,
			items: [
				{
					accrualDate: tranDate,
					accountChartId: currentAccountId,
					customerId,
					vendorId,
					debit: counterAccountAmount,
				},
				{
					accrualDate: tranDate,
					accountChartId: currentAccountId,
					customerId,
					vendorId,
					credit: counterAccountAmount,
				},
			],
		}
		const journalResult = await this.addJournal(data)
		await this.addItems(data, journalResult.id)
	}
}

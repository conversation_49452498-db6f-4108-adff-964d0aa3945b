import { Body, Inject, Post, Provide } from '@midwayjs/decorator'
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core'
import { TransJournalEntity } from '../../entity/journal'
import { TransJournalItemEntity } from '../../entity/journal_item'
import { TransManualEntity } from '../../entity/manual'
import { CustomerEntity } from '../../../customer/entity/customer'
import { VendorEntity } from '../../../vendor/entity/vendor'
import { AccountChartEntity } from '../../../account/entity/chart'
import { TransJournalService } from '../../service/journal'
import { SelectQueryBuilder } from 'typeorm'
import { TransJournalMiddleware } from '../../middleware/journal'
/**
 * 描述
 */
@Provide()
@CoolController(
	{
		api: ['add', 'delete', 'update', 'info', 'list', 'page'],
		entity: TransJournalEntity,
		service: TransJournalService,
		pageQueryOp: {
			fieldEq: [
				{ column: 'a.status', requestParam: 'status' },
				{ column: 'a.totalAmount', requestParam: 'totalAmount' },
			],
			keyWordLikeFields: ['a.journalNo', 'a.description'],
			select: [
				'a.*',
				'manual.tranNo',
				`JSON_ARRAYAGG(
      JSON_OBJECT(
        'itemId', item.id,
        'notes', item.notes,
        'debit', item.debit,
        'credit', item.credit,
        'currency', item.currency,
        'accrualDate', item.accrualDate,
        'journalId', item.journalId,
        'customerId', item.customerId,
        'customerNo', customer.customerNo,
        'vendorId', item.vendorId,
        'vendorNo', vendor.vendorNo,
        'accountChartId', item.accountChartId,
        'accountCode', chart.accountCode,
        'accountAlias', chart.accountAlias,
        'createBy', item.createBy,
        'updateBy', item.updateBy,
        'createTime', item.createTime,
        'updateTime', item.updateTime
      )
    ) AS items
    `,
			],
			join: [
				{
					entity: TransJournalItemEntity,
					alias: 'item',
					condition: 'a.id = item.journalId',
					type: 'leftJoin',
				},
				{
					entity: CustomerEntity,
					alias: 'customer',
					condition: 'item.customerId = customer.id',
					type: 'leftJoin',
				},
				{
					entity: VendorEntity,
					alias: 'vendor',
					condition: 'item.vendorId = vendor.id',
					type: 'leftJoin',
				},
				{
					entity: AccountChartEntity,
					alias: 'chart',
					condition: 'chart.id = item.accountChartId',
					type: 'leftJoin',
				},
				{
					entity: TransManualEntity,
					alias: 'manual',
					condition: 'a.transManualId = manual.id',
					type: 'leftJoin',
				},
			],
			extend: async (find: SelectQueryBuilder<TransJournalItemEntity>) => {
				find.groupBy('a.id')
			},
		},
	},
	{
		middleware: [TransJournalMiddleware],
	}
)
export class AdminJournalController extends BaseController {
	@Inject()
	service: TransJournalService

	@Post('/ops/add')
	async addJournal(@Body() body) {
		// 确保从请求体中获取新字段
		const journalData = {
			...body,
			originCurrentCurrency: body.originCurrentCurrency,
			originCurrentAmount: body.originCurrentAmount,
			originCounterCurrency: body.originCounterCurrency,
			originCounterAmount: body.originCounterAmount
		};
		
		const journal = await this.service.addJournal(journalData)
		const items = await this.service.addItems(body, journal?.id)
		return this.ok({
			...journal,
			items: items.generatedMaps,
		})
	}
}

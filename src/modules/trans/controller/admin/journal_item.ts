import { Provide } from '@midwayjs/decorator'
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core'
import { TransJournalItemEntity } from '../../entity/journal_item'
/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: TransJournalItemEntity,
	pageQueryOp: {
		fieldEq: ['journalId'],
	},
})
export class AdminJournalItemController extends BaseController {}

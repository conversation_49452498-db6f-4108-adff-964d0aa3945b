-- 为 op_trans_journal_item 表新增 invoiceId 和 billId 字段
-- 执行日期: 2025-07-12
-- 描述: 新增发票ID和账单ID字段，用于关联journal item与invoice和bill

-- 新增 billId 字段
ALTER TABLE `op_trans_journal_item`
ADD COLUMN `billId` int(11) NULL COMMENT '关联账单ID' AFTER `vendorId`;

-- 新增 invoiceId 字段
ALTER TABLE `op_trans_journal_item`
ADD COLUMN `invoiceId` int(11) NULL COMMENT '关联发票ID' AFTER `billId`;

-- 添加外键约束（可选，根据业务需求决定是否启用）
-- ALTER TABLE `op_trans_journal_item` 
-- ADD CONSTRAINT `fk_journal_item_invoice` 
-- FOREIGN KEY (`invoiceId`) REFERENCES `op_invoice` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- ALTER TABLE `op_trans_journal_item` 
-- ADD CONSTRAINT `fk_journal_item_bill` 
-- FOREIGN KEY (`billId`) REFERENCES `op_expense_bill` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 添加索引以提高查询性能
CREATE INDEX `idx_journal_item_invoice_id` ON `op_trans_journal_item` (`invoiceId`);
CREATE INDEX `idx_journal_item_bill_id` ON `op_trans_journal_item` (`billId`);

-- 验证字段是否添加成功
SELECT 'Fields added successfully!' as status;

-- 显示表结构
DESCRIBE `op_trans_journal_item`;
